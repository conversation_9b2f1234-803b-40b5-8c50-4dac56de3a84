

import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, ValidationPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { InventoryService } from './inventory.service';
import { InventoryDto, CreateInventoryDto, UpdateInventoryDto, RestockInventoryDto } from './dto/inventory.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { InventoryItem } from '../shared/types.shared'; // Import shared type

@Controller('inventory')
@UseGuards(JwtAuthGuard)
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Get()
  async findAll(): Promise<InventoryItem[]> {
    const items = await this.inventoryService.findAll();
    return items.map(item => ({
      id: item.product_id, // Map to match frontend InventoryItem interface
      productId: item.product_id,
      productName: item.product?.name || 'Unknown Product', // Include product name from relation
      stockQuantity: item.stock_quantity,
      lowStockThreshold: item.lowStockThreshold,
      lastRestockDate: item.lastRestockDate instanceof Date ? item.lastRestockDate.toISOString() : String(item.lastRestockDate),
    }));
  }

  @Post()
  @UseGuards(AdminRoleGuard) // Only Admins can add inventory items
  async create(@Body(new ValidationPipe()) createInventoryDto: CreateInventoryDto): Promise<InventoryItem> {
    const item = await this.inventoryService.create(createInventoryDto);
    return {
      id: item.product_id,
      productId: item.product_id,
      productName: item.product?.name || 'Unknown Product',
      stockQuantity: item.stock_quantity,
      lowStockThreshold: item.lowStockThreshold,
      lastRestockDate: item.lastRestockDate instanceof Date ? item.lastRestockDate.toISOString() : String(item.lastRestockDate),
    };
  }

  @Post('restock')
  @UseGuards(AdminRoleGuard) // Only Admins can restock inventory items
  async restock(@Body(new ValidationPipe()) restockInventoryDto: RestockInventoryDto): Promise<InventoryItem> {
    const item = await this.inventoryService.restock(restockInventoryDto);
    return {
      id: item.product_id,
      productId: item.product_id,
      productName: item.product?.name || 'Unknown Product',
      stockQuantity: item.stock_quantity,
      lowStockThreshold: item.lowStockThreshold,
      lastRestockDate: item.lastRestockDate instanceof Date ? item.lastRestockDate.toISOString() : String(item.lastRestockDate),
    };
  }

  @Put(':productId')
  @UseGuards(AdminRoleGuard) // Only Admins can update inventory items
  async update(
    @Param('productId') productId: string,
    @Body(new ValidationPipe()) updateInventoryDto: UpdateInventoryDto
  ): Promise<InventoryItem> {
    const item = await this.inventoryService.update(productId, updateInventoryDto);
    return {
      id: item.product_id,
      productId: item.product_id,
      productName: item.product?.name || 'Unknown Product',
      stockQuantity: item.stock_quantity,
      lowStockThreshold: item.lowStockThreshold,
      lastRestockDate: item.lastRestockDate instanceof Date ? item.lastRestockDate.toISOString() : String(item.lastRestockDate),
    };
  }

  @Delete(':productId')
  @UseGuards(AdminRoleGuard) // Only Admins can delete inventory items
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('productId') productId: string): Promise<void> {
    await this.inventoryService.remove(productId);
  }
}