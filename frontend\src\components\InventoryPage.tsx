import React, { useState, useEffect } from 'react';
import InventoryTable from './InventoryTable';
import AddInventoryModal from './AddInventoryModal';
import EditInventoryModal from './EditInventoryModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import Spinner from './common/Spinner';
import Alert from './common/Alert';
import { fetchInventory, addInventoryItem, updateInventoryItem, deleteInventoryItem } from '../services/dataService';
import { InventoryItem, User, UserRole } from '../types';

interface InventoryPageProps {
  currentUser: User | null;
}

const InventoryPage: React.FC<InventoryPageProps> = ({ currentUser }) => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [modalLoading, setModalLoading] = useState<boolean>(false);

  const loadInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      const items = await fetchInventory();
      setInventoryItems(items);
    } catch (err: any) {
      console.error("Failed to load inventory data:", err);
      setError(err.message || "Failed to load inventory data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInventory();
  }, []);

  // Modal handlers
  const handleAddNewItem = () => {
    setShowAddModal(true);
  };

  const handleEditItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowEditModal(true);
  };

  const handleDeleteItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowDeleteModal(true);
  };

  const handleAddSubmit = async (data: {
    productId: string;
    stockQuantity: number;
    lowStockThreshold: number;
    lastRestockDate?: string;
  }) => {
    try {
      setModalLoading(true);
      await addInventoryItem(data);
      await loadInventory();
      setShowAddModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.message || "Failed to add new item. Product ID might already have an inventory entry.");
    } finally {
      setModalLoading(false);
    }
  };

  const handleEditSubmit = async (data: {
    stockQuantity: number;
    lowStockThreshold: number;
    lastRestockDate: string;
  }) => {
    if (!selectedItem) return;

    try {
      setModalLoading(true);
      await updateInventoryItem(selectedItem.productId, data);
      await loadInventory();
      setShowEditModal(false);
      setSelectedItem(null);
      setError(null);
    } catch (err: any) {
      setError(err.message || "Failed to update inventory item.");
    } finally {
      setModalLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;

    try {
      setModalLoading(true);
      await deleteInventoryItem(selectedItem.productId);
      await loadInventory();
      setShowDeleteModal(false);
      setSelectedItem(null);
      setError(null);
    } catch (err: any) {
      setError(err.message || "Failed to delete inventory item.");
    } finally {
      setModalLoading(false);
    }
  };

  const closeModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setSelectedItem(null);
  };


  return (
    <div className="p-4 md:p-6 space-y-6 bg-slate-100 min-h-screen">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">Inventory Management</h2>
        {currentUser?.role === UserRole.ADMIN && (
           <button 
             onClick={handleAddNewItem}
             className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
           >
            Add New Item (Demo)
          </button>
        )}
      </div>
      
      {loading && <div className="flex justify-center pt-10"><Spinner /></div>}
      {error && <Alert message={error} type="error" onClose={() => setError(null)}/>}
      {!loading && !error && (
        <InventoryTable 
          items={inventoryItems} 
          showAdminActions={currentUser?.role === UserRole.ADMIN} 
        />
      )}
    </div>
  );
};

export default InventoryPage;