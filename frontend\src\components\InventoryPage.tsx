import React, { useState, useEffect } from 'react';
import InventoryTable from './InventoryTable';
import Spinner from './common/Spinner';
import Alert from './common/Alert';
import { fetchInventory, addInventoryItem } from '../services/dataService';
import { InventoryItem, User, UserRole } from '../types';

interface InventoryPageProps {
  currentUser: User | null;
}

const InventoryPage: React.FC<InventoryPageProps> = ({ currentUser }) => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      const items = await fetchInventory();
      setInventoryItems(items);
    } catch (err: any) {
      console.error("Failed to load inventory data:", err);
      setError(err.message || "Failed to load inventory data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInventory();
  }, []);

  const handleAddNewItem = async () => {
    if (currentUser?.role === UserRole.ADMIN) {
      // This is a placeholder for a form/modal to get new item details
      const newItemData = { 
        productId: `prod_new_${Date.now().toString().slice(-5)}`, // Example ID, in real app, get from product list or create new product
        stockQuantity: 0, // Default values
        lowStockThreshold: 10,
        lastRestockDate: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
      };
      try {
        setLoading(true); 
        // Send the data to the backend
        await addInventoryItem(newItemData); 
        // Reload inventory after adding new item
        await loadInventory(); 
        setError(null); // Clear previous errors on success
      } catch (err: any) {
        setError(err.message || "Failed to add new item. Product ID might already have an inventory entry.");
      } finally {
        setLoading(false);
      }
    }
  };


  return (
    <div className="p-4 md:p-6 space-y-6 bg-slate-100 min-h-screen">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">Inventory Management</h2>
        {currentUser?.role === UserRole.ADMIN && (
           <button 
             onClick={handleAddNewItem}
             className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
           >
            Add New Item (Demo)
          </button>
        )}
      </div>
      
      {loading && <div className="flex justify-center pt-10"><Spinner /></div>}
      {error && <Alert message={error} type="error" onClose={() => setError(null)}/>}
      {!loading && !error && (
        <InventoryTable 
          items={inventoryItems} 
          showAdminActions={currentUser?.role === UserRole.ADMIN} 
        />
      )}
    </div>
  );
};

export default InventoryPage;