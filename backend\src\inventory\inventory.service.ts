import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Inventory } from './entities/inventory.entity';
import { CreateInventoryDto, UpdateInventoryDto } from './dto/inventory.dto';
import { ProductsService } from '../products/products.service';

@Injectable()
export class InventoryService {
  private readonly logger = new Logger(InventoryService.name);

  constructor(
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    private productsService: ProductsService,
  ) {}

  async findAll(): Promise<Inventory[]> {
    return this.inventoryRepository.find({ relations: ['product'] });
  }

  async create(createInventoryDto: CreateInventoryDto): Promise<Inventory> {
    const product = await this.productsService.findOne(createInventoryDto.productId);
    if (!product) {
      throw new NotFoundException(`Product with ID "${createInventoryDto.productId}" not found.`);
    }

    const existingInventoryItem = await this.inventoryRepository.findOne({
      where: { product_id: createInventoryDto.productId }
    });

    if (existingInventoryItem) {
      throw new ConflictException(`Inventory item for product ID "${createInventoryDto.productId}" already exists.`);
    }

    const newInventoryItem = this.inventoryRepository.create({
      product_id: createInventoryDto.productId,
      stock_quantity: createInventoryDto.stockQuantity,
      lowStockThreshold: createInventoryDto.lowStockThreshold,
      lastRestockDate: createInventoryDto.lastRestockDate || new Date(),
      product: product
    });

    return this.inventoryRepository.save(newInventoryItem);
  }

  async update(productId: string, updateInventoryDto: UpdateInventoryDto): Promise<Inventory> {
    const existingInventoryItem = await this.inventoryRepository.findOne({
      where: { product_id: productId },
      relations: ['product']
    });

    if (!existingInventoryItem) {
      throw new NotFoundException(`Inventory item for product ID "${productId}" not found.`);
    }

    // Update only the fields that are provided
    if (updateInventoryDto.stockQuantity !== undefined) {
      existingInventoryItem.stock_quantity = updateInventoryDto.stockQuantity;
    }
    if (updateInventoryDto.lowStockThreshold !== undefined) {
      existingInventoryItem.lowStockThreshold = updateInventoryDto.lowStockThreshold;
    }
    if (updateInventoryDto.lastRestockDate !== undefined) {
      existingInventoryItem.lastRestockDate = new Date(updateInventoryDto.lastRestockDate);
    }

    return this.inventoryRepository.save(existingInventoryItem);
  }

  async remove(productId: string): Promise<void> {
    const existingInventoryItem = await this.inventoryRepository.findOne({
      where: { product_id: productId }
    });

    if (!existingInventoryItem) {
      throw new NotFoundException(`Inventory item for product ID "${productId}" not found.`);
    }

    await this.inventoryRepository.remove(existingInventoryItem);
  }
  
  async onModuleInit() {
    const inventoryCount = await this.inventoryRepository.count();
    if (inventoryCount === 0) {
      this.logger.log('No inventory items found. Seeding initial inventory...');
      try {
        // Ensure products are seeded first by accessing the service method that also seeds
        await this.productsService.onModuleInit(); // Ensure products are there
        const products = await this.productsService.findAll();

        if (products.length > 0) {
          for (const product of products) {
            const inventoryItem = this.inventoryRepository.create({
              product_id: product.id,
              stock_quantity: Math.floor(Math.random() * 80) + 20, // 20 to 100
              lowStockThreshold: Math.floor(Math.random() * 10) + 5, // 5 to 15
              lastRestockDate: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000), // Random date in last 60 days
              product: product
            });
            
            await this.inventoryRepository.save(inventoryItem);
          }
          
          this.logger.log(`${products.length} inventory items seeded.`);
        } else {
          this.logger.warn('No products found in DB to seed inventory for. Ensure products are seeded first.');
        }
      } catch (error) {
        this.logger.error(`Error seeding inventory: ${error.message}`, error.stack);
      }
    }
  }
}