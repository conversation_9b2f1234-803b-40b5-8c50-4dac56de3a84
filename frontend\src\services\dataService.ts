import { 
  Product, 
  InventoryItem, 
  SalesTrendData, 
  ProductCategoryDistribution, 
  CustomerDemographicData,
  User,
  AuditLogEntry,
  UserRole,
  SystemSettings as FrontendSystemSettings // Alias to avoid conflict with backend type name
} from '../types';
import { get, post, put, del } from './apiHelper';

const getToken = (): string | undefined => {
    const token = localStorage.getItem('authToken');
    return token ? token : undefined;
}

export const fetchProducts = (): Promise<Product[]> => get<Product[]>('/products');
export const fetchProductById = (id: string): Promise<Product | undefined> => get<Product | undefined>(`/products/${id}`);

interface DashboardMetrics {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
}
export const fetchDashboardMetrics = (): Promise<DashboardMetrics> => get<DashboardMetrics>('/dashboard/metrics', getToken());
export const fetchSalesTrends = (): Promise<SalesTrendData[]> => get<SalesTrendData[]>('/dashboard/sales-trends', getToken());
export const fetchTopProducts = (limit: number = 5): Promise<ProductCategoryDistribution[]> => get<ProductCategoryDistribution[]>(`/dashboard/top-products?limit=${limit}`, getToken());

interface CustomerDemographicsResponse {
    age: CustomerDemographicData[];
    gender: CustomerDemographicData[];
}
export const fetchCustomerDemographics = (): Promise<CustomerDemographicsResponse> => get<CustomerDemographicsResponse>('/dashboard/customer-demographics', getToken());

export const fetchInventory = (): Promise<InventoryItem[]> => get<InventoryItem[]>('/inventory', getToken());

// Define a proper payload type that matches the backend DTO
export interface CreateInventoryItemPayload {
    productId: string;
    stockQuantity: number;
    lowStockThreshold: number;
    lastRestockDate?: string;
}

export const addInventoryItem = (itemData: CreateInventoryItemPayload): Promise<InventoryItem> => {
    return post<CreateInventoryItemPayload, InventoryItem>('/inventory', itemData, getToken());
}


export interface HistoricalSalesData {
    date: string;
    sales: number;
}
export const fetchHistoricalSalesForProduct = (productId: string): Promise<HistoricalSalesData[]> => {
  return get<HistoricalSalesData[]>(`/products/${productId}/historical-sales`, getToken());
};

export const fetchAdminSystemUsers = (): Promise<User[]> => get<User[]>('/admin/users', getToken());

export const updateAdminUserRole = (userId: string, role: UserRole): Promise<User> => {
  return put<{role: UserRole}, User>(`/admin/users/${userId}/role`, { role }, getToken());
};

export const deleteAdminUser = (userId: string): Promise<void> => {
  return del<void>(`/admin/users/${userId}`, getToken());
};

// Use aliased FrontendSystemSettings for type safety
export const fetchAdminSystemSettings = (): Promise<FrontendSystemSettings> => get<FrontendSystemSettings>('/admin/settings', getToken());

export const updateAdminSystemSettings = (settings: FrontendSystemSettings): Promise<FrontendSystemSettings> => {
  return put<FrontendSystemSettings, FrontendSystemSettings>('/admin/settings', settings, getToken());
};

export const fetchAdminAuditLogs = (searchTerm?: string): Promise<AuditLogEntry[]> => {
  const query = searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : '';
  return get<AuditLogEntry[]>(`/admin/audit-logs${query}`, getToken());
};