
import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsN<PERSON>ber, IsDateString, Min, IsOptional } from 'class-validator';

export class InventoryDto {
  @IsString()
  @IsNotEmpty()
  product_id: string;

  @IsNumber()
  @Min(0)
  stock_quantity: number;

  @IsNumber()
  @Min(0)
  lowStockThreshold: number;

  @IsDateString()
  lastRestockDate: string;
}

export class CreateInventoryDto {
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsNumber()
  @Min(0)
  stockQuantity: number;

  @IsNumber()
  @Min(0)
  lowStockThreshold: number;

  @IsDateString()
  @IsOptional()
  lastRestockDate?: string;
}
