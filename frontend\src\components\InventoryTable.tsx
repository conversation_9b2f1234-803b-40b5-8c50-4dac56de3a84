import React from 'react';
import { InventoryItem } from '../types';
import Alert from './common/Alert';

interface InventoryTableProps {
  items: InventoryItem[];
  showAdminActions?: boolean;
}

const InventoryTable: React.FC<InventoryTableProps> = ({ items, showAdminActions }) => {
  if (items.length === 0) {
    return <p className="text-gray-500">No inventory items to display.</p>;
  }

  const lowStockItems = items.filter(item => item.stockQuantity <= item.lowStockThreshold);

  return (
    <div className="space-y-4">
      {lowStockItems.length > 0 && (
        <Alert
          type="warning"
          message={`${lowStockItems.length} item(s) are low on stock! (${lowStockItems.map(i => i.productName).join(', ')})`}
        />
      )}
      <div className="overflow-x-auto bg-white shadow-md rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Quantity</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Low Stock Threshold</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Restock</th>
              {showAdminActions && (
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {items.map((item) => {
              const isLowStock = item.stockQuantity <= item.lowStockThreshold;
              return (
                <tr key={item.productId} className={isLowStock ? 'bg-yellow-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.productName}</td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm ${isLowStock ? 'text-red-600 font-semibold' : 'text-gray-700'}`}>{item.stockQuantity}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{item.lowStockThreshold}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {isLowStock ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Low Stock</span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">In Stock</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{new Date(item.lastRestockDate).toLocaleDateString()}</td>
                  {showAdminActions && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                      <button 
                        onClick={() => alert(`Edit item: ${item.productName}`)} 
                        className="text-indigo-600 hover:text-indigo-900 font-medium"
                        aria-label={`Edit ${item.productName}`}
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => alert(`Delete item: ${item.productName}`)} 
                        className="text-red-600 hover:text-red-900 font-medium"
                        aria-label={`Delete ${item.productName}`}
                      >
                        Delete
                      </button>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default InventoryTable;